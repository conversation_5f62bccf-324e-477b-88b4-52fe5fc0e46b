package com.sanythadmin.project.workstudy.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import com.sanythadmin.common.enums.JudgeMark;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.project.workstudy.enums.AttendanceType;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 勤工助学/考勤记录表
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_QGZX_ATTENDANCE_RECORD")
@Entity
@Table(name = "SYT_QGZX_ATTENDANCE_RECORD")
public class QgzxAttendanceRecord implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 学生申请ID
     */
    @Column(name = "STUDENT_APPLY_ID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableField("STUDENT_APPLY_ID")
    private String studentApplyId;

    /**
     * 考勤日期
     */
    @Column(name = "ATTENDANCE_DATE")
    @TableField(value = "ATTENDANCE_DATE")
    private LocalDate attendanceDate;

    /**
     * 考勤类型（上班/下班）
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "ATTENDANCE_TYPE", columnDefinition = ColumnType.VARCHAR2_32)
    @TableField("ATTENDANCE_TYPE")
    private AttendanceType attendanceType;

    /**
     * 打卡时间
     */
    @Column(name = "CLOCK_TIME")
    @TableField("CLOCK_TIME")
    private LocalDateTime clockTime;

    /**
     * 打卡地点
     */
    @Column(name = "LOCATION", columnDefinition = ColumnType.VARCHAR2_512)
    @TableField("LOCATION")
    private String location;

    /**
     * 经度
     */
    @Column(name = "LONGITUDE", columnDefinition = "NUMBER(10,6)")
    @TableField("LONGITUDE")
    private BigDecimal longitude;

    /**
     * 纬度
     */
    @Column(name = "LATITUDE", columnDefinition = "NUMBER(10,6)")
    @TableField("LATITUDE")
    private BigDecimal latitude;

    /**
     * 工作时长（小时，保留2位小数）- 下班打卡时计算
     */
    @Column(name = "WORK_DURATION", columnDefinition = "NUMBER(8,2)")
    @TableField("WORK_DURATION")
    private BigDecimal workDuration;

    /**
     * 是否正常（是否在规定时间和地点范围内）
     */
    @Column(name = "IS_NORMAL", columnDefinition = ColumnType.NUMBER_1)
    @TableField("IS_NORMAL")
    private JudgeMark isNormal;

    /**
     * 备注
     */
    @Column(name = "REMARK", columnDefinition = ColumnType.VARCHAR2_512)
    @TableField("REMARK")
    private String remark;

    /**
     * 创建时间
     */
    @Column(name = "CREATE_TIME")
    @TableField("CREATE_TIME")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Column(name = "UPDATE_TIME")
    @TableField("UPDATE_TIME")
    private LocalDateTime updateTime;

    // 关联对象（不存储到数据库）
    
    /**
     * 学生信息
     */
    @Transient
    @TableField(exist = false)
    private UserInfo userInfo;

    /**
     * 学生申请信息
     */
    @Transient
    @TableField(exist = false)
    private QgzxStudentApply studentApply;

    /**
     * 岗位信息
     */
    @Transient
    @TableField(exist = false)
    private QgzxJobApplication jobApplication;
}
