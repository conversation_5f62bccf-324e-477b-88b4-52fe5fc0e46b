<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>打印标签预览测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background: #f5f7fa;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .test-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 20px;
            color: #2c3e50;
        }
        
        .label-preview {
            margin-bottom: 20px;
        }
        
        /* 复制优化后的标签样式 */
        .custom-label-item {
            position: relative;
            width: 200mm;
            height: 25mm;
            border: 2px solid #2c3e50;
            border-radius: 4px;
            margin-bottom: 12px;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            box-sizing: border-box;
        }
        
        .template-zone {
            position: absolute;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 4px;
            border-radius: 2px;
            line-height: 1.3;
            text-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }
        
        .logo-zone {
            left: 0mm;
            top: 0mm;
            width: 30mm;
            height: 25mm;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 1px solid #e9ecef;
            text-align: center;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .coin-info-zone {
            left: 30mm;
            top: 0mm;
            width: 120mm;
            height: 25mm;
            background: transparent;
            text-align: left;
            padding-left: 8px;
            justify-content: flex-start;
        }
        
        .grade-zone {
            left: 150mm;
            top: 0mm;
            width: 30mm;
            height: 25mm;
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border: 1px solid #ffeaa7;
            text-align: center;
            font-weight: bold;
        }
        
        .qr-zone {
            left: 180mm;
            top: 0mm;
            width: 20mm;
            height: 25mm;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border: 1px solid #dee2e6;
            text-align: center;
        }
        
        .bank-name {
            font-size: 14px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 2px;
        }
        
        .coin-name {
            font-size: 12px;
            font-weight: 600;
            color: #34495e;
            margin-bottom: 1px;
        }
        
        .year-info {
            font-size: 11px;
            color: #95a5a6;
            margin-bottom: 1px;
        }
        
        .serial-number {
            font-size: 11px;
            color: #7f8c8d;
        }
        
        .grade-score {
            font-size: 36px;
            font-weight: bold;
            color: #e74c3c;
            line-height: 0.9;
            margin-bottom: 1px;
        }
        
        .grade-level {
            font-size: 9px;
            color: #7f8c8d;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .qr-placeholder {
            width: 50px;
            height: 50px;
            border: 1px solid #2c3e50;
            margin: 0 auto 2px auto;
            display: flex;
            align-items: center;
            justify-content: center;
            background: repeating-linear-gradient(45deg, #000, #000 1px, #fff 1px, #fff 3px);
            border-radius: 2px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.2);
        }
        
        .qr-text {
            font-size: 8px;
            color: #7f8c8d;
            font-weight: 500;
            text-align: center;
            word-break: break-all;
            max-width: 60px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">打印标签预览效果测试</h1>
        
        <div class="label-preview">
            <h3>优化后的标签样式：</h3>
            
            <!-- 测试标签1 -->
            <div class="custom-label-item">
                <div class="template-zone logo-zone">
                    <div style="font-size: 18px; color: #d4af37;">CMG</div>
                    <div style="font-size: 8px; color: #606266;">中乾评级</div>
                </div>
                
                <div class="template-zone coin-info-zone">
                    <div class="bank-name">中国人民银行</div>
                    <div class="coin-name">1980年拾元</div>
                    <div class="year-info">FK000002</div>
                    <div class="serial-number">民族人物头像 周口</div>
                </div>
                
                <div class="template-zone grade-zone">
                    <div class="grade-score">68</div>
                    <div class="grade-level">Superb Gem Unc</div>
                    <div style="font-size: 8px; color: #95a5a6;">EPQ 真</div>
                </div>
                
                <div class="template-zone qr-zone">
                    <div class="qr-placeholder">
                        <span style="color: #fff; background: rgba(0,0,0,0.7); padding: 1px 2px; border-radius: 1px; font-size: 6px;">QR</span>
                    </div>
                    <div class="qr-text">ZK25070001</div>
                </div>
            </div>
            
            <!-- 测试标签2 -->
            <div class="custom-label-item">
                <div class="template-zone logo-zone">
                    <div style="font-size: 18px; color: #d4af37;">CMG</div>
                    <div style="font-size: 8px; color: #606266;">中乾评级</div>
                </div>
                
                <div class="template-zone coin-info-zone">
                    <div class="bank-name">中国人民银行</div>
                    <div class="coin-name">1980年拾元</div>
                    <div class="year-info">FK000004</div>
                    <div class="serial-number">民族人物头像 周口</div>
                </div>
                
                <div class="template-zone grade-zone">
                    <div class="grade-score">65</div>
                    <div class="grade-level">Gem Unc</div>
                    <div style="font-size: 8px; color: #95a5a6;">EPQ 真</div>
                </div>
                
                <div class="template-zone qr-zone">
                    <div class="qr-placeholder">
                        <span style="color: #fff; background: rgba(0,0,0,0.7); padding: 1px 2px; border-radius: 1px; font-size: 6px;">QR</span>
                    </div>
                    <div class="qr-text">ZK25070002</div>
                </div>
            </div>
        </div>
        
        <div style="margin-top: 30px; padding: 15px; background: #e8f4fd; border-radius: 4px;">
            <h4 style="margin: 0 0 10px 0; color: #2c3e50;">优化说明：</h4>
            <ul style="margin: 0; color: #5a6c7d;">
                <li>增强了标签边框和阴影效果，更加专业</li>
                <li>优化了评级信息的字体大小对比，数字更突出</li>
                <li>改进了区域背景色和渐变效果</li>
                <li>提升了二维码占位符的视觉效果</li>
                <li>优化了字体渲染和文字阴影</li>
                <li>增加了hover效果和过渡动画</li>
            </ul>
        </div>
    </div>
</body>
</html>
